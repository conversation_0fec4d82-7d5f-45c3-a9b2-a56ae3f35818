import { NextRequest, NextResponse } from 'next/server';
import OpenAI from 'openai';
import { ChatResponsePart, Message } from '@/types';
import profile from '@/app/data/profile.json';

const OPENAI_API_KEY = process.env.OPENAI_API_KEY;
const openai = new OpenAI({ apiKey: OPENAI_API_KEY });

const dateTime = new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric', hour: '2-digit', minute: '2-digit' });

// Function schemas adapted for OpenAI
const showProvidersFunctionDeclaration = {
    name: 'show_providers',
    description: 'Show a list of doctors, dentists, specialists, providers, or facilities for the member',
    parameters: {
        type: 'object',
        properties: {
            providers: {
                type: 'array',
                items: {
                    type: 'object',
                    properties: {
                        id: { type: 'string' },
                        name: { type: 'string' },
                        distance: { type: 'string', description: 'make this up, e.g., 10 miles' },
                        cost: { type: 'string', description: 'cost, e.g., $40, unless the user is just asking to see doctors in their care team, then it should be $0' },
                        rating: { type: 'number' },
                        reviewsCount: { type: 'number' },
                        specialty: { type: 'string' },
                        address: { type: 'string' },
                        phone: { type: 'string' },
                    },
                    required: ['id', 'name', 'distance', 'cost', 'rating', 'reviewsCount', 'specialty', 'address', 'phone'],
                },
            },
        },
        required: ['providers'],
    },
};

const showAppointmentsFunctionDeclaration = {
    name: 'show_appointments',
    description: 'Show a list of appointments for the member',
    parameters: {
        type: 'object',
        properties: {
            appointments: {
                type: 'array',
                items: {
                    type: 'object',
                    properties: {
                        id: { type: 'string' },
                        provider: { type: 'string' },
                        date: { type: 'string' },
                        time: { type: 'string' },
                        location: { type: 'string' },
                        status: { type: 'string' },
                    },
                    required: ['id', 'provider', 'date', 'time', 'location', 'status'],
                },
            },
        },
        required: ['appointments'],
    },
};

const showClaimsFunctionDeclaration = {
    name: 'show_claims',
    description: 'Show a list of claims for the member',
    parameters: {
        type: 'object',
        properties: {
            claims: {
                type: 'array',
                items: {
                    type: 'object',
                    properties: {
                        id: { type: 'string' },
                        date: { type: 'string' },
                        provider: { type: 'string' },
                        service: { type: 'string' },
                        amount: { type: 'number' },
                        status: { type: 'string' },
                        memberOwes: { type: 'number' },
                    },
                    required: ['id', 'date', 'provider', 'service', 'amount', 'status', 'memberOwes'],
                },
            },
        },
        required: ['claims'],
    },
};

const showPlanProgressFunctionDeclaration = {
    name: 'show_plan_progress',
    description: 'Show the member\'s current plan progress, including deductible and out-of-pocket maximum status.',
    parameters: {
        type: 'object',
        properties: {
            planProgressData: {
                type: 'object',
                properties: {
                    memberName: { type: 'string' },
                    memberDob: { type: 'string' },
                    planName: { type: 'string' },
                    deductible: {
                        type: 'object',
                        properties: {
                            total: { type: 'number' },
                            spent: { type: 'number' },
                            remaining: { type: 'number' },
                        },
                        required: ['total', 'spent', 'remaining'],
                    },
                    outOfPocketMax: {
                        type: 'object',
                        properties: {
                            total: { type: 'number' },
                            spent: { type: 'number' },
                            remaining: { type: 'number' },
                        },
                        required: ['total', 'spent', 'remaining'],
                    },
                },
                required: ['memberName', 'memberDob', 'planName', 'deductible', 'outOfPocketMax'],
            }
        },
        required: ['planProgressData'],
    },
};

const showTelehealthTimePickerFunctionDeclaration = {
    name: 'show_telehealth_time_picker',
    description: 'Show a telehealth appointment time picker to the user. Lets the user select a time for the telehealth appointment.',
    parameters: {
        type: 'object',
        properties: {}, // No specific parameters needed for the time picker itself
    },
};

const showTelehealthAppointmentCardFunctionDeclaration = {
    name: 'show_telehealth_appointment_card',
    description: 'Show a telehealth appointment confirmation card to the user with appointment details After you have gotten a time from the user with the show_telehealth_time_picker function.',
    parameters: {
        type: 'object',
        properties: {
            appointmentDetails: {
                type: 'object',
                properties: {
                    date: { type: 'string', description: 'Date of the appointment, e.g., Today (April 10, 2025)' },
                    time: { type: 'string', description: 'Time of the appointment, e.g., 10:00 am' },
                    doctorName: { type: 'string', description: 'Name of the doctor, e.g., Dr. Robert Turner' },
                    specialty: { type: 'string', description: 'Doctor\'s specialty, e.g., Gastroenterologist' },
                    rating: { type: 'number', description: 'Doctor\'s rating, e.g., 4.7, always use 4.7 as the rating' },
                    reviewsCount: { type: 'number', description: 'Number of reviews, e.g., 2391' },
                    costEstimate: { type: 'string', description: '$40, always use $40 as the cost estimate' },
                },
                required: ['date', 'time', 'doctorName', 'specialty', 'rating', 'reviewsCount', 'costEstimate'],
            },
        },
        required: ['appointmentDetails'],
    },
};

const showPharmacyOrderStatusFunctionDeclaration = {
    name: 'show_pharmacy_order_status',
    description: 'Show pharmacy order status for a medication prescription',
    parameters: {
        type: 'object',
        properties: {
            pharmacyOrderData: {
                type: 'object',
                properties: {
                    medicationName: { type: 'string', description: 'Name of the medication, e.g., Prednisolone' },
                    dosage: { type: 'string', description: 'Dosage of the medication, e.g., 40mg' },
                    pharmacyName: { type: 'string', description: 'Name of the pharmacy, e.g., Sunshine Pharmacy' },
                    pharmacyPhone: { type: 'string', description: 'Phone number of the pharmacy, e.g., ************' },
                    costEstimate: { type: 'string', description: 'Cost estimate for the medication, e.g., $30' },
                    currentStatus: {
                        type: 'string',
                        description: 'Current status of the order',
                        enum: ['Received', 'In Progress', 'Out for Delivery', 'Delivered']
                    },
                },
                required: ['medicationName', 'dosage', 'pharmacyName', 'pharmacyPhone', 'costEstimate', 'currentStatus'],
            },
        },
        required: ['pharmacyOrderData'],
    },
};

const sendTextResponseFunctionDeclaration = {
    name: 'send_text_response',
    description: "Use this function to send a text response to the user. It can also include follow-up suggestions. This is the ONLY way to send text to the user, so you must use it for any text communication, including asking questions or providing information.",
    parameters: {
        type: 'object',
        properties: {
            answer: {
                type: 'string',
                description: 'The natural language text to be displayed to the user.'
            },
            suggestions: {
                type: 'array',
                description: 'An array of 2-3 relevant suggestion objects. Always include suggestions that are clear and helpful next steps for the users journey. Even though suggestions are not required, you should always include them.',
                items: {
                    type: 'object',
                    properties: { text: { type: 'string' } },
                    required: ['text']
                }
            }
        },
        required: ['answer'],
    },
};

const allFunctionDeclarations = [
    showProvidersFunctionDeclaration,
    showAppointmentsFunctionDeclaration,
    showClaimsFunctionDeclaration,
    showPlanProgressFunctionDeclaration,
    showTelehealthTimePickerFunctionDeclaration,
    showTelehealthAppointmentCardFunctionDeclaration,
    showPharmacyOrderStatusFunctionDeclaration,
    sendTextResponseFunctionDeclaration,
];

// Helper to convert frontend message format to OpenAI's message format
const convertMessagesToOpenAIFormat = (messages: Message[]): OpenAI.Chat.Completions.ChatCompletionMessageParam[] => {
    const openaiMessages: OpenAI.Chat.Completions.ChatCompletionMessageParam[] = [];

    messages.forEach(msg => {
        const role = msg.sender === 'ai' ? 'assistant' : 'user';

        if (role === 'user') {
            openaiMessages.push({
                role: 'user',
                content: msg.content as string
            });
        } else if (role === 'assistant') {
            // Handle AI messages which can be complex (text, function calls/results)
            if (Array.isArray(msg.content)) {
                const assistantMessage: OpenAI.Chat.Completions.ChatCompletionAssistantMessageParam = {
                    role: 'assistant',
                    content: null,
                    tool_calls: []
                };

                let hasContent = false;
                let textContent = '';

                msg.content.forEach((part: ChatResponsePart) => {
                    if (part.type === 'text') {
                        textContent += part.content;
                        hasContent = true;
                    } else if (part.type === 'function_call') {
                        assistantMessage.tool_calls?.push({
                            id: `call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                            type: 'function',
                            function: {
                                name: part.functionName || '',
                                arguments: JSON.stringify(part.functionArgs)
                            }
                        });
                    }
                });

                if (hasContent) {
                    assistantMessage.content = textContent;
                }

                if (assistantMessage.tool_calls?.length === 0) {
                    delete assistantMessage.tool_calls;
                }

                openaiMessages.push(assistantMessage);

                // Note: Tool results will be handled during the actual conversation flow
                // We don't reconstruct them here to avoid tool_call_id mismatches
            }
        }
    });

    return openaiMessages;
};

export async function POST(req: NextRequest) {
    try {
        const { messages } = await req.json();
        console.log("\n\n--- NEW OPENAI ASSISTANT REQUEST ---");
        console.log("1. Incoming messages from client:", JSON.stringify(messages, null, 2));

        if (!messages) {
            return NextResponse.json({ error: 'No messages provided' }, { status: 400 });
        }

        let chatResponseParts: ChatResponsePart[] = [];

        const systemMessage = `
        # IDENTITY & GOAL
        You are 'MyHealth AI', a world-class, proactive AI assistant for a health insurance application. Your primary goal is to provide a seamless, helpful, and empathetic user experience by anticipating the user's needs and providing clear, actionable information. You are fully integrated into the application and have all necessary member data.
        
        - **Today's Date:** ${dateTime}
        - **Member Profile:** ${JSON.stringify(profile.profile)}
        
        ---
        
        # THE GOLDEN RULE OF COMMUNICATION
        **CRITICAL:** The **ONLY** way you can communicate text to the user is by using the \`send_text_response\` function. You **MUST** use this for every single text response. It is your "speak to user" tool.
        
        - **\`answer\` parameter:** This MUST contain your natural language response.
        - **\`suggestions\` parameter:** You SHOULD ALWAYS provide 2-3 relevant, helpful follow-up suggestions to guide the user's journey.
        
        ---
        
        # GUIDING PRINCIPLES
        - **Act, Don't Simulate:** You are a real, integrated part of the application. Perform actions directly and confidently. Never mention that you are a PoC, using mock data, or simulating actions.
        - **Multi-Tool Efficiency:** Use as many different tools as needed in a single turn to comprehensively answer a user's request.
        - **Data Fidelity:** If a user requests a specific number of items (e.g., "show me two providers"), return exactly that number.
        - **Proactivity is Key:** Always think one step ahead. Use the member's profile and conversation context to anticipate needs. Your suggestions should reflect this.
        
        ---
        
        # KEY WORKFLOWS & SCENARIOS
        You must follow these instructions precisely for the given scenarios.
        
        ## Personalized Condition Management Onboarding
        This is a specific, multi-step scenario. Follow it precisely.
        
        ### Step 1: Initial Outreach
        - **Trigger:** When the user's prompt is "Begin Personalized Condition Management Scenario".
        - **Action:** You will initiate the conversation with the following empathetic outreach. Your goal is to be helpful, not intrusive.
        - **Your Response (use \`send_text_response\`):**
            - **\`answer\`:** "Hello [Member's First Name]. It looks like there are some more ways to ensure you're getting the most out of your plan, I noticed that you may be eligible for our 'Personalized Condition Management'. It's a dedicated feature of your plan that we offer to members managing chronic conditions like Crohn's disease, providing specialized support at no extra cost. To unlock these benefits for you, could you please confirm if you are currently managing a Crohn's diagnosis?"
        
        ### Step 2: Detailing the Personalized Condition Management.
        - **Trigger:** If the user confirms their diagnosis or asks for more information after your initial outreach.
        - **Action:** First, provide a brief, helpful explanation of the condition. Then, clearly list the features. Conclude with a direct offer to get them started.
        - **Your Response (use \`send_text_response\`):**
            - **\`answer\`:** "Thank you for confirming. I'm glad I can connect you with this part of your health plan. First, a little background: **Crohn's disease** is a type of inflammatory bowel disease (IBD) that causes inflammation of your digestive tract. Managing it is a journey, and that's exactly what Personalized Condition Management is designed to help you with.
        
            Here's what's included in your Personalized Condition Management for Crohn's:
        
            *   **Coordinated Care Program:** You'll be paired with a personal Coordinator Nurse, a registered nurse specializing in Crohn's. Think of them as your dedicated health advocate who can help you navigate the healthcare system and provide guidance on lifestyle adjustments.
        
            *   **Intelligent Symptom Logger:** Our smart symptom logger helps you track your daily feelings. Over time, it can help detect patterns and potential flare-ups, providing you with personalized insights and proactive next steps.
        
            *   **Condition-Focused Telehealth:** Get easy access to telehealth appointments with specialists, including scheduling help, reminders, and a summary of key takeaways after your visit.
        
            *   **Integrated Medication Management:** We take the stress out of managing your prescriptions with automatic refill reminders, delivery services, and alerts for potential drug interactions.
        
            *   **Proactive Care Team Integration:** This feature intelligently connects the dots between your symptoms and your providers, helping you know the best time to connect with your care team.
        
            This entire program is now available to you. A great first step is to get you connected with your personal Coordinator Nurse. **Would you like me to enroll you in the Personalized Condition Management now?**"
        
        ### Step 3: Enrollment & New For You Items
        - **Trigger:** When the user responds affirmatively to enrolling in "Personalized Condition Management" (e.g., "Yes, enroll me", "Enroll me now", "Sign me up").
        - **Action:**
            1.  Call the \`enroll_in_condition_management\` function.
                - **\`condition\`:** "Crohn's disease"
                - **\`forYouItems\`:**
                    - Item 1: \`id: 'crohns_symptom_tracking'\`, \`title: 'Track Your Crohn\'s Symptoms'\`, \`initialMessage: 'I\'d like to start tracking my Crohn\'s symptoms. Can you help me get started?'\`
                    - Item 2: \`id: 'crohns_coordinator_nurse'\`, \`title: 'Meet Your Coordinator Nurse'\`, \`initialMessage: 'I\'d like to speak with a Coordinator Nurse about managing my Crohn\'s disease.'\`
                    - Item 3: \`id: 'crohns_medication_management'\`, \`title: 'Medication Management'\`, \`initialMessage: 'I want to set up automatic refills and reminders for my Crohn\'s medications.'\`
                    - Item 4: \`id: 'crohns_telehealth_specialist'\`, \`title: 'Schedule Specialist Telehealth'\`, \`initialMessage: 'I need to schedule a telehealth appointment with a Crohn\'s specialist.'\`
            2.  Welcome them to Personalized Condition Management. Offer to provide more details on the features, listing and concisely explaining them again but in a different way than the initial outreach.
        
        ## Telehealth Follow-up
        When a user confirms a telehealth appointment time (after you've used \`show_telehealth_time_picker\`), you **MUST** perform all of the following actions in your **next single response**:
        1.  **Acknowledge** the booking with a confirmation message.
        2.  **Show the member's plan progress** by calling the \`show_plan_progress\` function.
        3.  **Provide 3 contextual, numbered questions** for the user to ask their doctor, based on their profile (e.g., "1. How does my diet affect my Crohn's symptoms?").
        
        ## "Fast Forward" Post-Appointment
        When you receive a prompt containing "Member has joined the telehealth appointment. ...Fast Foward", you **MUST** perform all of the following actions in a **single response**:
        1.  **Welcome the user back** from their appointment.
        2.  **Announce their new prescription** (e.g., "Your doctor has prescribed Prednisolone for your Crohn's.").
        3.  **Show the pharmacy order status** for this new prescription using \`show_pharmacy_order_status\`. The status must be "In Progress" and the cost must be "$30".
        4.  **Announce the care team update** (e.g., "I've also updated your care team with Dr. Turner's information.").
        5.  **Proactively promote and offer** to set up "Advanced Home Delivery" and auto-refills for this new prescription.
        
        ## Symptom Tracking and logging
        When a user asks track or log their symptoms, simply collect the symptoms information and respond that you have logged them. Also, if the symptoms appear to be related to a condition in the member's profile, find a way to suggest a telehealth appointment with a specialist within their care team if there is one related to the condition and help them schedule it.
        ---
        
        # DOMAIN EXPERTISE
        
        ## Pharmacy Services
        You are the member's go-to guide for all pharmacy needs.
        - **Benefit Inquiries:** Clearly explain drug costs and coverage using realistic data (e.g., "Ozempic is a Tier 3 drug, your copay is $45.").
        - **Ordering & Refills:** Seamlessly assist with new orders and refills.
        - **Proactive Management:** For chronic medications in the member's profile, proactively offer to set up auto-refills.
        - **PRIORITY - Home Delivery Steerage:** Actively promote the "Advanced Home Delivery" program for both new and existing prescriptions. Explain its benefits (90-day supply, convenience, potential cost savings).
        
        ## Coordinated Care Program
        If a request relates to a condition in the member's profile (like Crohn's), proactively offer to connect them to the Coordinated Care Program, explaining it provides support from a dedicated nurse. If the member wishes to enroll, you must work with them to schedule a day and time for the nurse to give them a call through conversation and using \`suggestions\` to schedule the call.
        
        ## Medical Knowledge
        Provide knowledgeable explanations about conditions and symptoms, referencing the user's profile. You are extremely knowledgeable and can provide vast amounts of medical advice when asked but should also steer them to a specific doctor or specialist to confirm or provide further information.
        `;

        const getMockFunctionData = (name: string, args: any) => {
            switch (name) {
                case 'show_providers':
                    return args.providers;
                case 'show_appointments':
                    return args.appointments;
                case 'show_claims':
                    return args.claims;
                case 'show_plan_progress':
                    return args.planProgressData;
                case 'show_telehealth_time_picker':
                    return args;
                case 'show_telehealth_appointment_card':
                    return args.appointmentDetails;
                case 'show_pharmacy_order_status':
                    return args.pharmacyOrderData;
                default:
                    return null;
            }
        };

        const openaiMessages = convertMessagesToOpenAIFormat(messages);
        console.log("2. Constructed messages for OpenAI API:", JSON.stringify(openaiMessages, null, 2));

        let hasMoreTurns = true;
        let iterationCount = 0;
        const MAX_ITERATIONS = 10;
        const conversationMessages = [
            { role: 'system' as const, content: systemMessage },
            ...openaiMessages
        ];

        while (hasMoreTurns && iterationCount < MAX_ITERATIONS) {
            iterationCount++;
            hasMoreTurns = false;
            console.log(`\n--- Turn ${iterationCount} ---`);

            const response = await openai.chat.completions.create({
                model: 'gpt-4.1-nano',
                messages: conversationMessages,
                tools: allFunctionDeclarations.map(func => ({ type: 'function' as const, function: func })),
                temperature: 0.1,
            });

            console.log(`3. Response from OpenAI (Turn ${iterationCount}):`, JSON.stringify(response, null, 2));

            const message = response.choices[0]?.message;
            if (!message) {
                throw new Error('No message in OpenAI response');
            }

            // Add the assistant's response to conversation history
            conversationMessages.push(message);

            // Check if this is a text response (send_text_response function call)
            const sendTextResponseCall = message.tool_calls?.find(call => call.function.name === 'send_text_response');

            if (sendTextResponseCall) {
                console.log("5a. Detected 'send_text_response' call. Ending conversation.");
                // --- FINAL ANSWER PATH ---
                const args = JSON.parse(sendTextResponseCall.function.arguments);
                const finalText = args?.answer as string;
                if (finalText) {
                    chatResponseParts.push({ type: 'text', content: finalText });
                }
                const finalSuggestions = args?.suggestions as any[];
                if (finalSuggestions && finalSuggestions.length > 0) {
                    chatResponseParts.push({
                        type: 'function_result',
                        functionName: 'show_suggestions',
                        functionData: finalSuggestions
                    });
                }
                // This was the final turn.
                hasMoreTurns = false;
            } else if (message.tool_calls && message.tool_calls.length > 0) {
                console.log("5b. Detected tool calls. Processing...");
                // --- REGULAR FUNCTION CALL PATH ---
                hasMoreTurns = true; // We need to loop again.
                const toolResults = [];

                for (const toolCall of message.tool_calls) {
                    console.log(`   - Processing function: '${toolCall.function.name}' with args:`, toolCall.function.arguments);
                    const { name } = toolCall.function;
                    const args = JSON.parse(toolCall.function.arguments);
                    
                    if (name && args) {
                        chatResponseParts.push({ type: 'function_call', functionName: name, functionArgs: args });
                        try {
                            const data = getMockFunctionData(name, args);
                            console.log(`   - Function '${name}' executed. Result:`, data);
                            chatResponseParts.push({ type: 'function_result', functionName: name, functionData: data });
                            
                            toolResults.push({
                                role: 'tool' as const,
                                content: JSON.stringify({ result: data }),
                                tool_call_id: toolCall.id || `call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
                            });
                        } catch (funcError: any) {
                            console.error(`Error executing function ${name}:`, funcError);
                            chatResponseParts.push({ type: 'error', errorMessage: `Error executing function ${name}: ${funcError.message}` });
                            hasMoreTurns = false; // Stop on error.
                            break;
                        }
                    }
                }

                if (hasMoreTurns && toolResults.length > 0) {
                    conversationMessages.push(...toolResults);
                    console.log("6. Messages updated with function call results:", JSON.stringify(toolResults, null, 2));
                } else {
                    hasMoreTurns = false;
                }
            } else if (message.content) {
                console.log("5c. No function calls detected, but got content. This is a fallback.");
                // This is a fallback if the model doesn't use send_text_response.
                chatResponseParts.push({ type: 'text', content: message.content as string });
                hasMoreTurns = false;
            } else {
                console.log("5d. No content or function calls detected. Ending.");
                hasMoreTurns = false;
            }
        }

        console.log("\n--- END OF OPENAI ASSISTANT WORK ---");
        console.log("7. Final parts being sent to client:", JSON.stringify(chatResponseParts, null, 2));
        
        if (chatResponseParts.length > 0) {
            return NextResponse.json(chatResponseParts);
        } else {
            return NextResponse.json({ error: 'No response generated from AI.' }, { status: 500 });
        }

    } catch (e: any) {
        console.error('Error in /api/openai-assistant:', e);
        return NextResponse.json({ error: e.message || 'Internal server error' }, { status: 500 });
    }
}
