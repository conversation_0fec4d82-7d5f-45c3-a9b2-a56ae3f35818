'use client';

import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';
import { useState, useEffect } from 'react';
import { useChat } from '@/contexts/ChatContext';
import { createMockChatThread, createMockTranscript1, createMockTranscript2 } from './mockData';
import { Transcript } from '@/types';

export default function OmnichannelPage() {
  const router = useRouter();
  const { createThread } = useChat();
  const [isLoading, setIsLoading] = useState(false);

  // Check if we should navigate to journeys after a reload
  useEffect(() => {
    const shouldNavigate = localStorage.getItem('navigateToJourneys');
    if (shouldNavigate === 'true') {
      localStorage.removeItem('navigateToJourneys');
      router.push('/journeys');
    }
  }, [router]);

  const handleLoadData = async () => {
    setIsLoading(true);

    try {
      console.log('Loading mock data...');

      // Create mock chat thread
      const mockThread = createMockChatThread();

      // Create mock transcripts
      const mockTranscript1 = createMockTranscript1();
      const mockTranscript2 = createMockTranscript2();

      // Load existing data from localStorage
      const existingThreads = JSON.parse(localStorage.getItem('chatThreads') || '[]');
      const existingTranscripts = JSON.parse(localStorage.getItem('transcripts') || '[]');

      // Add mock data to existing data (prepend to show as most recent)
      const updatedThreads = [mockThread, ...existingThreads];
      const updatedTranscripts = [mockTranscript2, mockTranscript1, ...existingTranscripts];

      // Save updated data to localStorage
      localStorage.setItem('chatThreads', JSON.stringify(updatedThreads));
      localStorage.setItem('transcripts', JSON.stringify(updatedTranscripts));

      console.log('Mock data loaded successfully!');

      // Set a flag to indicate we should navigate to journeys after reload
      localStorage.setItem('navigateToJourneys', 'true');

      // Reload the page to pick up the new data
      window.location.reload();
    } catch (error) {
      console.error('Error loading mock data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4 max-w-4xl mx-auto ">
      <div className="text-left space-y-8 w-full">
        <div className="space-y-4">
          <h1 className="text-3xl font-bold">A Seamless Chat & Voice Care Journey</h1>
          
          <p className="text-lg leading-relaxed">
            Follow a member, Emily, on her journey from flu-like symptoms to an asthma diagnosis. This demo showcases a proactive Virtual Concierge that seamlessly switches between <span className="font-semibold">chat and voice</span> to create a <span className="font-semibold">Tailored Experience</span> that simplifies healthcare. By proactively <span className="font-semibold">Guiding Decisions</span> and providing clear next steps, the assistant works to <span className="font-semibold">Build Trust</span> from the very first interaction.
          </p>
        </div>

        <div className="space-y-6">
          <h2 className="text-xl font-semibold">You will see:</h2>
          
          <ul className="text-left space-y-4 ">
            <li className="flex flex-col gap-2 border-b pb-4">
              <span className="font-semibold min-w-fit">Guided Decisions and Building Trust</span>
              <span>by starting in Chat to triage symptoms and explain care options, then switching to Voice to easily find and schedule a convenient Urgent Care appointment.</span>
            </li>
            
            <li className="flex flex-col gap-2 border-b pb-4">
              <span className="font-semibold min-w-fit">Delivering proactive Follow-up Care</span>
              <span>after the visit, handling <span className="font-semibold">Pharmacy Care</span> by arranging prescription home delivery and informing Emily of the <span className="font-semibold">Utilization Management (UM)</span> approval for a specialist, eliminating wait times.</span>
            </li>
            
            <li className="flex flex-col gap-2 border-b pb-4">
              <span className="font-semibold min-w-fit">Providing Easy Access to Benefits and Improving Health Literacy</span>
              <span>by solving a real-world emergency via a second voice call, leveraging Emily's transportation Benefits to book a ride and ensure she doesn't miss her crucial appointment.</span>
            </li>
          </ul>
        </div>
        
        <p className="text-muted-foreground italic mt-6 text-sm">
          Click "Load Data" to see this connected care experience in action.
        </p>
        
        <Button 
          onClick={handleLoadData}
          disabled={isLoading}
          size="lg"
          className="px-8 py-3 mt-0"
        >
          {isLoading ? 'Loading Data...' : 'Load Data'}
        </Button>
      </div>
    </div>
  );
}
