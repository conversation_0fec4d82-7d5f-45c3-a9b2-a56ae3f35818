'use client';

import React, { useState, useRef, useCallback } from 'react';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Send, Loader2, ArrowUp } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useChat } from '@/contexts/ChatContext';
import { useIsMobile } from '@/hooks/use-mobile';
import { useRouter } from 'next/navigation';
import { initialStaticSuggestions } from '@/app/data/Static_Suggestions';
import ChatSuggestions from './ChatSuggestions';
import { ShineBorder } from "@/components/magicui/shine-border";


interface WelcomeTextAreaProps {
  placeholder?: string;
}

export function WelcomeTextArea({ placeholder = "Ask me anything..." }: WelcomeTextAreaProps) {
  const [currentMessage, setCurrentMessage] = useState('');
  const [isSending, setIsSending] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const isMobile = useIsMobile();
  const router = useRouter();
  const { createThread, threads } = useChat();
  const deviceConfig = {
    isMobile,
    buttonSize: isMobile ? "h-11 w-11" : "h-10 w-10",
    inputAreaPadding: isMobile ? "p-3" : "p-4",
    autoFocus: !isMobile,
  };

  const handleInputChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    setCurrentMessage(event.target.value);
   
  };

  const handleSubmit = (event?: React.FormEvent<HTMLFormElement>) => {
    if (event) {
      event.preventDefault();
    }
    if (currentMessage.trim()) {
      setIsSending(true);
      
      createThread({ name: `New Chat ${threads.length + 1}`, initialMessage: currentMessage });

      router.push('/journeys');
      setCurrentMessage('');
      setIsSending(false);
      textareaRef.current?.focus();
    }
  };

  const handleSuggestionClick = useCallback(async (suggestionText: string) => {
    createThread({ name: `New Chat ${threads.length + 1}`, initialMessage: suggestionText });
    router.push('/journeys');
  }, [createThread, router, threads]);

  return (
    <div className={cn(
      "w-full max-w-2xl ",
      deviceConfig.inputAreaPadding
    )}>
     <div className="relative rounded-3xl"> 
        
      <form
        onSubmit={handleSubmit}
        className="flex items-end flex-col gap-2 rounded-3xl bg-white dark:bg-zinc-800 shadow-xl px-4 pt-2 pb-4"
        role="form"
        id="VC_Input_Landing"
        aria-label="Welcome message form"
        
      >
        <Textarea
          placeholder={placeholder}
          value={currentMessage}
          onChange={handleInputChange}
          onKeyDown={(e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
              e.preventDefault();
              handleSubmit();
            }
          }}
          className="flex-grow w-full min-h-[40px] resize-none border-0 focus-visible:ring-0 focus-visible:ring-offset-0 text-base shadow-none bg-transparent dark:text-white"
          
          ref={textareaRef}
          autoFocus={deviceConfig.autoFocus}
          aria-label="Message input"
          maxLength={2000}
          rows={1} // Adjusted for better initial appearance based on image
        />
        <div className="flex flex-row items-center justify-end gap-2 w-full">
          {/* <Button 
            // Example of an attachment button if needed in the future, like the paperclip in the image
            type="button"
            size="icon"
            variant="ghost"
            className={cn(deviceConfig.buttonSize, "text-zinc-500 hover:text-zinc-700 dark:text-zinc-400 dark:hover:text-zinc-200")}
            aria-label="Attach file"
          >
            <Paperclip className="h-5 w-5" />
          </Button> */}
          {/* <Button 
            // Example of a model selector if needed in the future
            type="button"
            variant="ghost"
            className="text-zinc-700 dark:text-zinc-300"
          >
            <Sparkles className="h-4 w-4 mr-2" /> Standard <ChevronDown className="h-4 w-4 ml-1" />
          </Button> */}
          <div className="flex-grow"></div> {/* Pushes the send button to the right */}
          <Button
            type="submit"
            id="send-button"
            size="icon"
            disabled={!currentMessage.trim() || isSending}
            className={cn(
              deviceConfig.buttonSize,
              "bg-zinc-200 hover:bg-zinc-300", // Adjusted style to match image (greyish button)
              "transition-all duration-200",
              "disabled:opacity-50"
            )}
            aria-label={isSending ? "Sending message" : "Send message"}
          >
            {isSending ? (
              <Loader2 className="h-5 w-5 animate-spin text-zinc-600 dark:text-zinc-400" />
            ) : (
              <ArrowUp className="h-5 w-5 text-zinc-600" />
            )}
          </Button>
          
        </div>
        
      </form>
      <ShineBorder shineColor={["#C78FFF", "#38D7FF"]} borderWidth={2} />
      </div>
      <div className="flex flex-col items-center justify-center mt-8">
        <ChatSuggestions suggestions={initialStaticSuggestions} onSuggestionClick={handleSuggestionClick} />
      </div>
    </div>
  );
} 